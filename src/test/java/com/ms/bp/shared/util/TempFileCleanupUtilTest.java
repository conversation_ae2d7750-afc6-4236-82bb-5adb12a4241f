package com.ms.bp.shared.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TempFileCleanupUtilクラスの単体テスト
 * 一時ファイルクリーンアップ機能の各種シナリオをテストする
 */
@DisplayName("TempFileCleanupUtil 単体テスト")
class TempFileCleanupUtilTest {

    @TempDir
    Path tempDir;

    private String originalProfile;

    @BeforeEach
    void setUp() {
        // 元の環境変数を保存
        originalProfile = System.getenv("SPRING_PROFILES_ACTIVE");
    }

    @AfterEach
    void tearDown() {
        // 環境変数を元に戻す（実際にはJVMレベルでは変更できないが、テスト用の設定をクリア）
        if (originalProfile != null) {
            System.setProperty("spring.profiles.active", originalProfile);
        } else {
            System.clearProperty("spring.profiles.active");
        }
    }



    @Test
    @DisplayName("正常ケース：テスト環境でのファイルクリーンアップ - 削除スキップ")
    void testCleanupExportTempFiles_TestEnvironment() throws IOException {
        // テスト用プロファイルを設定
        System.setProperty("spring.profiles.active", "test");
        
        // テスト用ファイルを作成
        Path csvFile1 = tempDir.resolve("test1.csv");
        Path csvFile2 = tempDir.resolve("test2.csv");
        Path zipFile = tempDir.resolve("test.zip");
        
        Files.createFile(csvFile1);
        Files.createFile(csvFile2);
        Files.createFile(zipFile);
        
        List<Path> csvFiles = Arrays.asList(csvFile1, csvFile2);
        
        // 実行
        TempFileCleanupUtil.cleanupExportTempFiles(csvFiles, zipFile);
        
        // 検証：テスト環境ではファイルが削除されない
        assertTrue(Files.exists(csvFile1));
        assertTrue(Files.exists(csvFile2));
        assertTrue(Files.exists(zipFile));
    }

    @Test
    @DisplayName("正常ケース：本番環境でのファイルクリーンアップ - 削除実行")
    void testCleanupExportTempFiles_ProductionEnvironment() throws IOException {
        // 本番用プロファイルを設定
        System.setProperty("spring.profiles.active", "prod");
        
        // テスト用ファイルを作成
        Path csvFile1 = tempDir.resolve("test1.csv");
        Path csvFile2 = tempDir.resolve("test2.csv");
        Path zipFile = tempDir.resolve("test.zip");
        
        Files.createFile(csvFile1);
        Files.createFile(csvFile2);
        Files.createFile(zipFile);
        
        List<Path> csvFiles = Arrays.asList(csvFile1, csvFile2);
        
        // 実行
        TempFileCleanupUtil.cleanupExportTempFiles(csvFiles, zipFile);
        
        // 検証：本番環境ではファイルが削除される
        assertFalse(Files.exists(csvFile1));
        assertFalse(Files.exists(csvFile2));
        assertFalse(Files.exists(zipFile));
    }

    @Test
    @DisplayName("正常ケース：nullパラメータでのクリーンアップ")
    void testCleanupExportTempFiles_NullParameters() {
        // 本番用プロファイルを設定
        System.setProperty("spring.profiles.active", "prod");
        
        // 実行：例外が発生しないことを確認
        assertDoesNotThrow(() -> {
            TempFileCleanupUtil.cleanupExportTempFiles(null, null);
        });
    }

    @Test
    @DisplayName("正常ケース：空のCSVファイルリストでのクリーンアップ")
    void testCleanupExportTempFiles_EmptyList() throws IOException {
        // 本番用プロファイルを設定
        System.setProperty("spring.profiles.active", "prod");
        
        // ZIPファイルのみ作成
        Path zipFile = tempDir.resolve("test.zip");
        Files.createFile(zipFile);
        
        // 実行
        TempFileCleanupUtil.cleanupExportTempFiles(Arrays.asList(), zipFile);
        
        // 検証：ZIPファイルは削除される
        assertFalse(Files.exists(zipFile));
    }

    @Test
    @DisplayName("異常ケース：存在しないファイルのクリーンアップ")
    void testCleanupExportTempFiles_NonExistentFiles() {
        // 本番用プロファイルを設定
        System.setProperty("spring.profiles.active", "prod");
        
        // 存在しないファイルパスを作成
        Path csvFile = tempDir.resolve("nonexistent.csv");
        Path zipFile = tempDir.resolve("nonexistent.zip");
        
        List<Path> csvFiles = Arrays.asList(csvFile);
        
        // 実行：例外が発生しないことを確認
        assertDoesNotThrow(() -> {
            TempFileCleanupUtil.cleanupExportTempFiles(csvFiles, zipFile);
        });
    }
}

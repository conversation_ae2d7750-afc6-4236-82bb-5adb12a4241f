package com.ms.bp.shared.common.util;

import com.amazonaws.services.lambda.runtime.Context;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Lambda実行時間後台監視クラス
 * 導入・導出処理専用の後台スレッド監視機能を提供
 *
 * 注意：このクラスはAWS Lambda環境での使用を前提としており、
 * マルチスレッド環境での複雑性とリスクを含んでいます。
 * データベース更新は回調函数機制により呼び出し元に委譲されます。
 */
public class BackgroundTimeoutMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(BackgroundTimeoutMonitor.class);
    
    /**
     * デフォルトのタイムアウト緩衝時間（ミリ秒）
     * 状態更新とクリーンアップ処理に必要な時間を確保
     */
    private static final long DEFAULT_BUFFER_TIME_MS = 30000; // 30秒
    
    /**
     * 監視間隔（ミリ秒）
     * 30秒ごとに残り時間をチェック
     */
    private static final long MONITORING_INTERVAL_MS = 30000; // 30秒
    
    /**
     * Lambda実行コンテキスト
     */
    private final Context lambdaContext;
    
    /**
     * ジョブID（履歴番号）
     */
    private final String jobId;

    /**
     * タイムアウト緩衝時間（ミリ秒）
     */
    private final long bufferTimeMs;

    /**
     * 監視スレッド停止フラグ
     * volatile修飾子により、スレッド間での可視性を保証
     */
    private volatile boolean shouldStop = false;

    /**
     * タイムアウト検出フラグ
     * AtomicBooleanによりスレッドセーフな操作を保証
     */
    private final AtomicBoolean timeoutDetected = new AtomicBoolean(false);

    /**
     * 監視スレッド参照
     * 適切なクリーンアップのために保持
     */
    private Thread monitoringThread;

    /**
     * 監視開始時刻（ミリ秒）
     */
    private final long startTimeMs;

    /**
     * タイムアウト検出時の回調函数
     * 呼び出し元がデータベース更新などの処理を実行
     */
    private final Consumer<String> timeoutCallback;
    
    /**
     * コンストラクタ
     *
     * @param lambdaContext Lambda実行コンテキスト
     * @param jobId ジョブID（履歴番号）
     * @param timeoutCallback タイムアウト検出時の回调函数
     */
    public BackgroundTimeoutMonitor(Context lambdaContext, String jobId, Consumer<String> timeoutCallback) {
        this(lambdaContext, jobId, timeoutCallback, DEFAULT_BUFFER_TIME_MS);
    }

    /**
     * カスタム緩衝時間を指定するコンストラクタ
     *
     * @param lambdaContext Lambda実行コンテキスト
     * @param jobId ジョブID（履歴番号）
     * @param timeoutCallback タイムアウト検出時の回调函数
     * @param bufferTimeMs タイムアウト緩衝時間（ミリ秒）
     */
    public BackgroundTimeoutMonitor(Context lambdaContext, String jobId, Consumer<String> timeoutCallback, long bufferTimeMs) {
        if (lambdaContext == null) {
            throw new IllegalArgumentException("Lambda contextは必須です");
        }
        if (jobId == null || jobId.trim().isEmpty()) {
            throw new IllegalArgumentException("ジョブIDは必須です");
        }
        if (timeoutCallback == null) {
            throw new IllegalArgumentException("タイムアウト回调函数は必須です");
        }
        if (bufferTimeMs < 0) {
            throw new IllegalArgumentException("緩衝時間は0以上である必要があります");
        }

        this.lambdaContext = lambdaContext;
        this.jobId = jobId;
        this.timeoutCallback = timeoutCallback;
        this.bufferTimeMs = bufferTimeMs;
        this.startTimeMs = System.currentTimeMillis();

        logger.debug("BackgroundTimeoutMonitor初期化: jobId={}, bufferTime={}ms",
                    jobId, bufferTimeMs);
    }
    
    /**
     * 後台監視を開始
     * 独立したデーモンスレッドで定期的なタイムアウトチェックを実行
     */
    public void startMonitoring() {
        if (monitoringThread != null && monitoringThread.isAlive()) {
            logger.warn("監視スレッドは既に実行中です: jobId={}", jobId);
            return;
        }
        
        monitoringThread = new Thread(this::monitoringLoop, "TimeoutMonitor-" + jobId);
        monitoringThread.setDaemon(true); // デーモンスレッドとして設定
        monitoringThread.start();

        logger.info("後台タイムアウト監視開始: jobId={}", jobId);
    }
    
    /**
     * 監視ループの実装
     * 定期的に残り時間をチェックし、タイムアウト接近時に適切な処理を実行
     */
    private void monitoringLoop() {
        try {
            while (!shouldStop && !timeoutDetected.get()) {
                long remainingTimeMs = lambdaContext.getRemainingTimeInMillis();

                logger.debug("タイムアウト監視チェック: jobId={}, remainingTime={}ms",
                           jobId, remainingTimeMs);

                // タイムアウト接近判定
                if (remainingTimeMs <= bufferTimeMs) {
                    handleTimeoutApproaching(remainingTimeMs);
                    break;
                }

                // 監視間隔待機
                Thread.sleep(MONITORING_INTERVAL_MS);
            }
        } catch (InterruptedException e) {
            logger.info("監視スレッドが中断されました: jobId={}", jobId);
            Thread.currentThread().interrupt(); // 中断状態を復元
        } catch (Exception e) {
            logger.error("監視スレッドでエラーが発生しました: jobId={}", jobId, e);
        } finally {
            logger.debug("監視スレッド終了: jobId={}", jobId);
        }
    }
    
    /**
     * タイムアウト接近時の処理
     * 回调函数を呼び出してタイムアウト処理を委譲
     *
     * @param remainingTimeMs 残り実行時間（ミリ秒）
     */
    private void handleTimeoutApproaching(long remainingTimeMs) {
        timeoutDetected.set(true);
        long elapsedTimeMs = System.currentTimeMillis() - startTimeMs;

        logger.warn("Lambda実行時間タイムアウト接近を検出: jobId={}, " +
                   "remainingTime={}ms, elapsedTime={}ms",
                   jobId, remainingTimeMs, elapsedTimeMs);

        // 回调函数を呼び出してタイムアウト処理を委譲
        try {
            timeoutCallback.accept(jobId);
            logger.info("タイムアウト回调函数実行完了: jobId={}", jobId);
        } catch (Exception e) {
            logger.error("タイムアウト回调函数実行エラー: jobId={}", jobId, e);
        }

        logger.info("タイムアウト接近により処理を中断しました: jobId={}", jobId);
    }
    

    /**
     * タイムアウトが検出されたかを確認
     * 
     * @return タイムアウトが検出された場合はtrue
     */
    public boolean isTimeoutDetected() {
        return timeoutDetected.get();
    }

    /**
     * リソースクリーンアップ
     * 監視スレッドの停止と関連リソースの解放
     */
    public void cleanup() {
        shouldStop = true;

        if (monitoringThread != null && monitoringThread.isAlive()) {
            try {
                // スレッドの終了を待機（最大5秒）
                monitoringThread.interrupt();
                monitoringThread.join(5000);

                if (monitoringThread.isAlive()) {
                    logger.warn("監視スレッドの終了タイムアウト: jobId={}", jobId);
                } else {
                    logger.debug("監視スレッドが正常に終了しました: jobId={}", jobId);
                }
            } catch (InterruptedException e) {
                logger.warn("監視スレッド終了待機が中断されました: jobId={}", jobId);
                Thread.currentThread().interrupt();
            }
        }
    }
}

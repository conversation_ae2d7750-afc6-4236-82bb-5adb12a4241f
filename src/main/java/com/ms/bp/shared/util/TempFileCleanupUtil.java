package com.ms.bp.shared.util;

import com.ms.bp.shared.common.constants.BusinessConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.stream.Stream;

/**
 * 一時ファイルクリーンアップユーティリティ
 * エクスポート処理で生成された一時ファイルの安全な削除を担当
 */
public class TempFileCleanupUtil {
    private static final Logger logger = LoggerFactory.getLogger(TempFileCleanupUtil.class);

    /**
     * エクスポート処理で生成された一時ファイルを安全にクリーンアップ
     *
     * @param csvFiles 削除対象のCSVファイルリスト
     * @param zipFile 削除対象のZIPファイル
     */
    public static void cleanupExportTempFiles(List<Path> csvFiles, Path zipFile) {
        // テスト環境チェック
        var activeProfile = System.getenv(BusinessConstants.ENV_PROFILE);

        // テスト環境では削除をスキップ
        if (activeProfile == null || activeProfile.trim().isEmpty()) {
            logger.info("テスト環境のため一時ファイルの削除をスキップします。CSV: {}, ZIP: {}",
                    csvFiles != null ? csvFiles.size() : 0,
                    zipFile != null ? zipFile.getFileName() : "null");
            return;
        }

        logger.info("一時ファイルのクリーンアップを開始します");

        // CSVファイルを削除
        Stream.ofNullable(csvFiles)
                .flatMap(List::stream)
                .forEach(csvFile -> deleteFile(csvFile, "CSV"));

        // ZIPファイルと一時ディレクトリを削除
        if (zipFile != null) {
            deleteFile(zipFile, "ZIP");
        }

        logger.info("一時ファイルのクリーンアップが完了しました");
    }

    /**
     * ファイルを安全に削除
     *
     * @param file 削除対象のファイル
     * @param fileType ファイルタイプ（ログ出力用）
     */
    private static void deleteFile(Path file, String fileType) {
        try {
            if (Files.deleteIfExists(file)) {
                logger.debug("{}ファイルを削除しました: {}", fileType, file.getFileName());
            }
        } catch (IOException e) {
            logger.warn("{}ファイルの削除に失敗しました {}: {}", fileType, file.getFileName(), e.getMessage());
        }
    }
}